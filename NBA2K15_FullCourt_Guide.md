# NBA 2K15 Full Court Shots Cheat Table Guide

## Overview
This guide explains how to enable full court shots in NBA 2K15 using Cheat Engine. The standard 3-point sliders only work up to a certain range because the game has multiple layers of shot calculation that need to be modified.

## Files Included
1. `NBA2K15_FullCourt_Shots.CT` - New dedicated full court shots table
2. `Haxozaur's NBA 2K15 MyPlayer Table V1.CT` - Modified original table with added full court features

## How NBA 2K15 Shot Mechanics Work

### Standard Shot Calculation Layers:
1. **Base Success Rate** - Your shooting attributes (Standing Shot Three, etc.)
2. **Slider Multipliers** - The sliders you see in the original table (3PT Success, etc.)
3. **Distance Penalty** - Additional penalty based on distance from basket
4. **Shot Type Detection** - Game determines if shot is allowed at that range
5. **Arc/Power Calculations** - Physics calculations for shot trajectory

### Why 3PT Sliders Don't Work for Full Court:
- The 3PT Success slider only affects shots within the "3-point range"
- Beyond a certain distance, the game applies separate distance penalties
- Full court shots may be classified as a different shot type entirely

## Setup Instructions

### Method 1: Using the New Dedicated Table
1. Open Cheat Engine
2. Attach to NBA2K15.exe process
3. Load `NBA2K15_FullCourt_Shots.CT`
4. Start with the "BASIC SHOT SLIDERS" section:
   - Set 3PT Success to 1.0
   - Set Mid-Range Success to 1.0
   - Set Close Shot Success to 1.0
5. Then activate "FULL COURT SHOT MECHANICS":
   - Shot Distance Penalty to 0
   - Max Shot Range to 999
   - Half Court Shot Multiplier to 1
   - Full Court Shot Multiplier to 1
   - Shot Type Override to 1

### Method 2: Using the Modified Original Table
1. Load the modified `Haxozaur's NBA 2K15 MyPlayer Table V1.CT`
2. Navigate to "USER SLIDERS" → "FULL COURT SHOT MODS"
3. Activate the entries as described above

## Testing and Troubleshooting

### Step-by-Step Testing:
1. **Start Small**: First test with just the 3PT Success slider at 1.0
2. **Add Distance Override**: Enable "Shot Distance Penalty" set to 0
3. **Increase Range**: Set "Max Shot Range" to 999
4. **Enable Full Court**: Activate the full court multipliers
5. **Test Gradually**: Try shots from progressively further distances

### If Shots Still Miss:
1. **Check Address Validity**: The addresses may be different for your game version
2. **Try Different Values**: 
   - Distance Penalty: Try values like 0, 0.1, 0.5
   - Shot Multipliers: Try values like 1.5, 2.0, 5.0
3. **Memory Scanning**: Use Cheat Engine to find the actual addresses

### Finding Correct Addresses:
1. Take a shot and note the success/failure
2. Use "Unknown initial value" scan
3. Take another shot with different result
4. Use "Changed value" or "Unchanged value" scan
5. Repeat until you find the shot calculation addresses

## Advanced Modifications

### Custom Distance Penalties:
- Set different penalties for different ranges
- Half court: 0.8 multiplier (80% success)
- Full court: 0.5 multiplier (50% success)

### Shot Power Adjustments:
- Increase "Shot Power Multiplier" if shots fall short
- Typical values: 1.5 - 3.0 for full court shots

### Experimental Features:
The "EXPERIMENTAL ADDRESSES" section contains addresses that may or may not work:
- Shot Arc Override: Modifies shot trajectory
- Distance Fade Disable: Removes distance-based accuracy fade
- Extreme Range Enable: Allows shots from any distance

## Important Notes

### Game Version Compatibility:
- These addresses are for a specific version of NBA 2K15
- Steam, retail, and cracked versions may have different addresses
- You may need to adjust addresses using pointer scans

### Memory Address Explanation:
- `nba2k15.exe+32A123C` = Base address + offset
- If addresses don't work, the base offset may be different
- Use Cheat Engine's pointer scan feature to find correct addresses

### Safety Tips:
- Always backup your save files before using cheats
- Test in practice mode first
- Some modifications may cause game crashes

## Troubleshooting Common Issues

### "Address not found" errors:
1. Make sure NBA 2K15 is running
2. Check if you're using the correct game version
3. Try running Cheat Engine as administrator

### Shots still have normal range limitations:
1. The game may be using different addresses for shot calculations
2. Try scanning for the actual shot success values during gameplay
3. Look for additional distance penalty calculations

### Game crashes:
1. Reduce the values (don't set everything to maximum)
2. Enable cheats one at a time to identify problematic entries
3. Some addresses may be read-only or protected

## Next Steps for Customization

If the provided addresses don't work perfectly:
1. Use the original working addresses as a reference
2. Perform memory scans during actual gameplay
3. Look for patterns in nearby memory addresses
4. Create pointer scans for more reliable address finding

The key is understanding that full court shots require modifying multiple game systems, not just the basic shooting sliders.
