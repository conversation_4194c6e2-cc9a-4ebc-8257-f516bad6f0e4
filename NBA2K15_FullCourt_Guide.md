# NBA 2K15 Full Court Shots Cheat Table Guide

## Overview
This guide explains how to enable full court shots in NBA 2K15 using Cheat Engine. Based on analysis of a working NBA 2K23 infinite range table, this uses **assembly code injection** to intercept and modify the game's shot calculation logic in real-time.

## Files Included
1. `NBA2K15_FullCourt_Shots.CT` - Assembly injection method (based on NBA 2K23 working code)
2. `NBA2K15_InfiniteRange_Alternative.CT` - Alternative assembly approach with generic patterns
3. `Haxozaur's NBA 2K15 MyPlayer Table V1.CT` - Modified original table with added full court features

## Key Insight from NBA 2K23 Reference
The working NBA 2K23 table revealed that **simple memory value changes don't work** for infinite range shots. Instead, you need to:
1. **Intercept shot range detection** - Hook the code that determines shot type
2. **Override shot success calculation** - Force shots to succeed when they would normally fail due to distance

## How NBA 2K15 Shot Mechanics Work

### Standard Shot Calculation Layers:
1. **Base Success Rate** - Your shooting attributes (Standing Shot Three, etc.)
2. **Slider Multipliers** - The sliders you see in the original table (3PT Success, etc.)
3. **Distance Penalty** - Additional penalty based on distance from basket
4. **Shot Type Detection** - Game determines if shot is allowed at that range
5. **Arc/Power Calculations** - Physics calculations for shot trajectory

### Why 3PT Sliders Don't Work for Full Court:
- The 3PT Success slider only affects shots within the "3-point range"
- Beyond a certain distance, the game applies separate distance penalties
- Full court shots may be classified as a different shot type entirely

## Setup Instructions

### Method 1: Assembly Injection (Recommended - Based on Working NBA 2K23 Code)
1. Open Cheat Engine
2. Attach to NBA2K15.exe process
3. Load `NBA2K15_FullCourt_Shots.CT`
4. **IMPORTANT**: Activate in this exact order:
   - First: Click "NBA 2K15 INFINITE RANGE - ACTIVATE FIRST"
   - Second: Click "INFINITE RANGE SHOT SUCCESS - ACTIVATE SECOND"
5. If successful, you should be able to shoot from anywhere on the court

### Method 2: Alternative Assembly Approach
1. If Method 1 fails, try `NBA2K15_InfiniteRange_Alternative.CT`
2. This uses more generic assembly patterns that might work better
3. Activate "NBA 2K15 INFINITE RANGE - ALTERNATIVE METHOD"

### Method 3: Fallback Basic Sliders
1. If assembly methods fail, use the "FALLBACK: BASIC SLIDERS" section
2. This is the old approach but may provide some improvement

## Testing and Troubleshooting

### Step-by-Step Testing:
1. **Start Small**: First test with just the 3PT Success slider at 1.0
2. **Add Distance Override**: Enable "Shot Distance Penalty" set to 0
3. **Increase Range**: Set "Max Shot Range" to 999
4. **Enable Full Court**: Activate the full court multipliers
5. **Test Gradually**: Try shots from progressively further distances

### If Shots Still Miss:
1. **Check Address Validity**: The addresses may be different for your game version
2. **Try Different Values**: 
   - Distance Penalty: Try values like 0, 0.1, 0.5
   - Shot Multipliers: Try values like 1.5, 2.0, 5.0
3. **Memory Scanning**: Use Cheat Engine to find the actual addresses

### Finding Correct Addresses:
1. Take a shot and note the success/failure
2. Use "Unknown initial value" scan
3. Take another shot with different result
4. Use "Changed value" or "Unchanged value" scan
5. Repeat until you find the shot calculation addresses

## How the NBA 2K23 Method Works

### Understanding the Assembly Injection:
The working NBA 2K23 table uses two injection points:

1. **Range Detection Hook**:
   ```assembly
   aobscanmodule(INJECT2,NBA2K23.exe,8B 41 0C FF C8 83 F8 01 77)
   ```
   - Finds the code that checks shot range/type
   - Stores the shot type in a custom variable
   - Pattern: `mov eax,[rcx+0C]; dec eax; cmp eax,01; ja`

2. **Success Override Hook**:
   ```assembly
   aobscanmodule(INJECT,NBA2K23.exe,89 B0 3C 02 00 00 48)
   ```
   - Finds where shot success is written to memory
   - Checks if it's a long-range shot (type 2)
   - Forces success value to 1 if it is
   - Pattern: `mov [rax+0000023C],esi`

### Adapting for NBA 2K15:
The challenge is finding equivalent patterns in NBA 2K15:
- Assembly patterns will be different due to different game version
- Memory offsets will be different
- But the logic should be similar

## Manual Pattern Finding for NBA 2K15

If the provided tables don't work, here's how to find the correct patterns:

### Finding Shot Range Detection:
1. Use Cheat Engine's "Memory View" → "Tools" → "Auto Assemble"
2. Use "Template" → "AOB Injection"
3. Search for patterns like:
   - `8B ?? 0C FF C8` (mov eax,[reg+0C]; dec eax)
   - `83 F8 ?? 77` (cmp eax,value; ja)
   - `F3 0F 10 ?? ?? ?? ?? ??` (movss xmm,distance)

### Finding Shot Success Assignment:
1. Look for patterns like:
   - `89 ?? ?? ?? ?? ??` (mov [memory],register)
   - `C7 ?? ?? ?? ?? ?? 01 00 00 00` (mov [memory],1)
   - Near shot calculation code

### Testing Your Patterns:
1. Set breakpoints on your found addresses
2. Take shots in-game and see if they trigger
3. Examine the registers and memory to understand the data flow

## Advanced Modifications

### Custom Distance Penalties:
- Set different penalties for different ranges
- Half court: 0.8 multiplier (80% success)
- Full court: 0.5 multiplier (50% success)

### Shot Power Adjustments:
- Increase "Shot Power Multiplier" if shots fall short
- Typical values: 1.5 - 3.0 for full court shots

### Experimental Features:
The "EXPERIMENTAL ADDRESSES" section contains addresses that may or may not work:
- Shot Arc Override: Modifies shot trajectory
- Distance Fade Disable: Removes distance-based accuracy fade
- Extreme Range Enable: Allows shots from any distance

## Important Notes

### Game Version Compatibility:
- These addresses are for a specific version of NBA 2K15
- Steam, retail, and cracked versions may have different addresses
- You may need to adjust addresses using pointer scans

### Memory Address Explanation:
- `nba2k15.exe+32A123C` = Base address + offset
- If addresses don't work, the base offset may be different
- Use Cheat Engine's pointer scan feature to find correct addresses

### Safety Tips:
- Always backup your save files before using cheats
- Test in practice mode first
- Some modifications may cause game crashes

## Troubleshooting Common Issues

### "Address not found" errors:
1. Make sure NBA 2K15 is running
2. Check if you're using the correct game version
3. Try running Cheat Engine as administrator

### Shots still have normal range limitations:
1. The game may be using different addresses for shot calculations
2. Try scanning for the actual shot success values during gameplay
3. Look for additional distance penalty calculations

### Game crashes:
1. Reduce the values (don't set everything to maximum)
2. Enable cheats one at a time to identify problematic entries
3. Some addresses may be read-only or protected

## Next Steps for Customization

If the provided addresses don't work perfectly:
1. Use the original working addresses as a reference
2. Perform memory scans during actual gameplay
3. Look for patterns in nearby memory addresses
4. Create pointer scans for more reliable address finding

The key is understanding that full court shots require modifying multiple game systems, not just the basic shooting sliders.
