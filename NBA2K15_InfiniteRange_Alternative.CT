<?xml version="1.0" encoding="utf-8"?>
<CheatTable CheatEngineTableVersion="42">
  <CheatEntries>
    <CheatEntry>
      <ID>0</ID>
      <Description>"NBA 2K15 INFINITE RANGE - ALTERNATIVE METHOD"</Description>
      <Options moHideChildren="1" moActivateChildrenAsWell="1" moDeactivateChildrenAsWell="1"/>
      <VariableType>Auto Assembler Script</VariableType>
      <AssemblerScript>{ Game   : NBA2K15.exe
  Version: 1.0
  Date   : 2025-01-18
  Author : AI Assistant
  
  Alternative approach using more generic patterns
  This searches for common shot calculation patterns in NBA 2K15
}

[ENABLE]

// Try to find shot distance calculation
aobscanmodule(SHOT_DISTANCE,NBA2K15.exe,F3 0F 10 ?? ?? ?? ?? ?? F3 0F 5C) // Distance calculation pattern
alloc(newmem_dist,$1000,"NBA2K15.exe"+400000)
alloc(infinite_range_flag,4)
registersymbol(infinite_range_flag)

label(code_dist)
label(return_dist)

newmem_dist:
code_dist:
  movss xmm0,[rax+00000000]  // Original distance load
  mov [infinite_range_flag],1 // Mark that we're in shot calculation
  movss xmm1,xmm0            // Backup original distance
  mov eax,0x41200000          // Load 10.0 as float (close range)
  movd xmm0,eax               // Override distance to close range
  subss xmm0,xmm1             // Original subtraction
  jmp return_dist

SHOT_DISTANCE:
  jmp newmem_dist
  nop
  nop
return_dist:
registersymbol(SHOT_DISTANCE)

[DISABLE]

SHOT_DISTANCE:
  db F3 0F 10 ?? ?? ?? ?? ?? F3 0F 5C

unregistersymbol(SHOT_DISTANCE)
unregistersymbol(infinite_range_flag)
dealloc(newmem_dist)
dealloc(infinite_range_flag)

</AssemblerScript>
      <CheatEntries>
        <CheatEntry>
          <ID>1</ID>
          <Description>"SHOT SUCCESS OVERRIDE"</Description>
          <VariableType>Auto Assembler Script</VariableType>
          <AssemblerScript>{ Game   : NBA2K15.exe
  Version: 1.0
  Date   : 2025-01-18
  
  Override shot success calculation
}

[ENABLE]

// Look for shot success assignment
aobscanmodule(SHOT_SUCCESS,NBA2K15.exe,89 ?? ?? ?? ?? ?? 48 8B) // Success assignment pattern
alloc(newmem_success,$1000,SHOT_SUCCESS,"NBA2K15.exe"+400000)

label(code_success)
label(return_success)
label(force_make)

newmem_success:
code_success:
  cmp [infinite_range_flag],1
  je force_make
  mov [rax+00000000],esi      // Original code (address will be auto-detected)
  jmp return_success

force_make:
  mov esi,1                   // Force shot to go in
  mov [rax+00000000],esi
  mov [infinite_range_flag],0 // Reset flag
  jmp return_success

SHOT_SUCCESS:
  jmp newmem_success
  nop
return_success:
registersymbol(SHOT_SUCCESS)

[DISABLE]

SHOT_SUCCESS:
  db 89 ?? ?? ?? ?? ?? 48 8B

unregistersymbol(SHOT_SUCCESS)
dealloc(newmem_success)

</AssemblerScript>
        </CheatEntry>
      </CheatEntries>
    </CheatEntry>
    <CheatEntry>
      <ID>3000</ID>
      <Description>"MANUAL MEMORY SCANNING GUIDE"</Description>
      <Options moHideChildren="1"/>
      <LastState Value="" Activated="0" RealAddress="00000000"/>
      <Color>8080FF</Color>
      <GroupHeader>1</GroupHeader>
      <CheatEntries>
        <CheatEntry>
          <ID>3001</ID>
          <Description>"Step 1: Find Shot Success Address"</Description>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>8080FF</Color>
          <VariableType>4 Bytes</VariableType>
          <Address>00000000</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>3002</ID>
          <Description>"Step 2: Find Distance Calculation"</Description>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>8080FF</Color>
          <VariableType>Float</VariableType>
          <Address>00000000</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>3003</ID>
          <Description>"Step 3: Find Range Limit Check"</Description>
          <LastState Value="??" Activated="0" RealAddress="00000000"/>
          <Color>8080FF</Color>
          <VariableType>Float</VariableType>
          <Address>00000000</Address>
        </CheatEntry>
      </CheatEntries>
    </CheatEntry>
  </CheatEntries>
  <UserdefinedSymbols/>
</CheatTable>
