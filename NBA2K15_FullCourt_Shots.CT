<?xml version="1.0" encoding="utf-8"?>
<CheatTable CheatEngineTableVersion="42">
  <CheatEntries>
    <CheatEntry>
      <ID>0</ID>
      <Description>"NBA 2K15 INFINITE RANGE - ACTIVATE FIRST"</Description>
      <Options moHideChildren="1" moActivateChildrenAsWell="1" moDeactivateChildrenAsWell="1"/>
      <VariableType>Auto Assembler Script</VariableType>
      <AssemblerScript>{ Game   : NBA2K15.exe
  Version: 1.0
  Date   : 2025-01-18
  Author : AI Assistant

  NBA 2K15 Infinite Range Shot Detection
  Based on NBA 2K23 working implementation
}

[ENABLE]

aobscanmodule(INJECT_RANGE,NBA2K15.exe,8B 41 0C FF C8 83 F8 01 77) // Shot range check pattern
alloc(newmem_range,$1000,"NBA2K15.exe"+400000)
alloc(shot_type_flag,4)
registersymbol(shot_type_flag)

label(code_range)
label(return_range)

newmem_range:

code_range:
  mov eax,[rcx+0C]
  mov [shot_type_flag],eax
  dec eax
  jmp return_range

INJECT_RANGE:
  jmp newmem_range
return_range:
registersymbol(INJECT_RANGE)

[DISABLE]

INJECT_RANGE:
  db 8B 41 0C FF C8

unregistersymbol(INJECT_RANGE)
unregistersymbol(shot_type_flag)
dealloc(newmem_range)
dealloc(shot_type_flag)

</AssemblerScript>
      <CheatEntries>
        <CheatEntry>
          <ID>1</ID>
          <Description>"INFINITE RANGE SHOT SUCCESS - ACTIVATE SECOND"</Description>
          <VariableType>Auto Assembler Script</VariableType>
          <AssemblerScript>{ Game   : NBA2K15.exe
  Version: 1.0
  Date   : 2025-01-18
  Author : AI Assistant

  NBA 2K15 Shot Success Override for Infinite Range
}

[ENABLE]

aobscanmodule(INJECT_SUCCESS,NBA2K15.exe,89 B0 3C 02 00 00 48) // Shot success assignment pattern
alloc(newmem_success,$1000,INJECT_SUCCESS,"NBA2K15.exe"+400000)

label(code_success)
label(return_success)
label(force_success)

newmem_success:

code_success:
  cmp [shot_type_flag],2  // Check if long range shot
  je force_success
  mov [rax+0000023C],esi  // Original code
  jmp return_success

force_success:
  mov esi,1               // Force shot success
  mov [rax+0000023C],esi
  jmp return_success

INJECT_SUCCESS:
  jmp newmem_success
  nop
return_success:
registersymbol(INJECT_SUCCESS)

[DISABLE]

INJECT_SUCCESS:
  db 89 B0 3C 02 00 00

unregistersymbol(INJECT_SUCCESS)
dealloc(newmem_success)

</AssemblerScript>
        </CheatEntry>
      </CheatEntries>
    </CheatEntry>
    <CheatEntry>
      <ID>2000</ID>
      <Description>"FALLBACK: BASIC SLIDERS (If Assembly Fails)"</Description>
      <Options moHideChildren="1"/>
      <LastState Value="" Activated="0" RealAddress="00000000"/>
      <Color>FF8000</Color>
      <GroupHeader>1</GroupHeader>
      <CheatEntries>
        <CheatEntry>
          <ID>2001</ID>
          <Description>"3PT Success (Set to 1.0)"</Description>
          <LastState Value="1" Activated="0" RealAddress="00000000"/>
          <Color>FF8000</Color>
          <VariableType>Float</VariableType>
          <Address>nba2k15.exe+32A123C</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>2002</ID>
          <Description>"Mid-Range Success (Set to 1.0)"</Description>
          <LastState Value="1" Activated="0" RealAddress="00000000"/>
          <Color>FF8000</Color>
          <VariableType>Float</VariableType>
          <Address>nba2k15.exe+32A1234</Address>
        </CheatEntry>
        <CheatEntry>
          <ID>2003</ID>
          <Description>"Close Shot Success (Set to 1.0)"</Description>
          <LastState Value="1" Activated="0" RealAddress="00000000"/>
          <Color>FF8000</Color>
          <VariableType>Float</VariableType>
          <Address>nba2k15.exe+32A122C</Address>
        </CheatEntry>
      </CheatEntries>
    </CheatEntry>
  </CheatEntries>
  <UserdefinedSymbols/>
</CheatTable>
        <CheatEntry>
          <ID>1001</ID>
          <Description>"BASIC SHOT SLIDERS"</Description>
          <Options moHideChildren="1"/>
          <LastState Value="" Activated="0" RealAddress="00000000"/>
          <Color>80000008</Color>
          <GroupHeader>1</GroupHeader>
          <CheatEntries>
            <CheatEntry>
              <ID>1002</ID>
              <Description>"3PT Success (Set to 1.0)"</Description>
              <LastState Value="1" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A123C</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>1003</ID>
              <Description>"Mid-Range Success (Set to 1.0)"</Description>
              <LastState Value="1" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A1234</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>1004</ID>
              <Description>"Close Shot Success (Set to 1.0)"</Description>
              <LastState Value="1" Activated="0" RealAddress="00000000"/>
              <Color>FF8000</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A122C</Address>
            </CheatEntry>
          </CheatEntries>
        </CheatEntry>
        <CheatEntry>
          <ID>2000</ID>
          <Description>"FULL COURT SHOT MECHANICS"</Description>
          <Options moHideChildren="1"/>
          <LastState Value="" Activated="0" RealAddress="00000000"/>
          <Color>00FF00</Color>
          <GroupHeader>1</GroupHeader>
          <CheatEntries>
            <CheatEntry>
              <ID>2001</ID>
              <Description>"Shot Distance Penalty (Set to 0)"</Description>
              <LastState Value="0" Activated="0" RealAddress="00000000"/>
              <Color>00FF00</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A1300</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>2002</ID>
              <Description>"Max Shot Range (Set to 999)"</Description>
              <LastState Value="999" Activated="0" RealAddress="00000000"/>
              <Color>00FF00</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A1304</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>2003</ID>
              <Description>"Half Court Shot Multiplier (Set to 1)"</Description>
              <LastState Value="1" Activated="0" RealAddress="00000000"/>
              <Color>00FF00</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A1308</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>2004</ID>
              <Description>"Full Court Shot Multiplier (Set to 1)"</Description>
              <LastState Value="1" Activated="0" RealAddress="00000000"/>
              <Color>00FF00</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A130C</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>2005</ID>
              <Description>"Shot Type Override (Set to 1)"</Description>
              <LastState Value="1" Activated="0" RealAddress="00000000"/>
              <Color>00FF00</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+32A1310</Address>
            </CheatEntry>
          </CheatEntries>
        </CheatEntry>
        <CheatEntry>
          <ID>3000</ID>
          <Description>"EXPERIMENTAL ADDRESSES"</Description>
          <Options moHideChildren="1"/>
          <LastState Value="" Activated="0" RealAddress="00000000"/>
          <Color>FF00FF</Color>
          <GroupHeader>1</GroupHeader>
          <CheatEntries>
            <CheatEntry>
              <ID>3001</ID>
              <Description>"Shot Arc Override (Set to 0)"</Description>
              <LastState Value="0" Activated="0" RealAddress="00000000"/>
              <Color>FF00FF</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A1338</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>3002</ID>
              <Description>"Shot Power Multiplier (Set to 2)"</Description>
              <LastState Value="2" Activated="0" RealAddress="00000000"/>
              <Color>FF00FF</Color>
              <VariableType>Float</VariableType>
              <Address>nba2k15.exe+32A133C</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>3003</ID>
              <Description>"Distance Fade Disable (Set to 1)"</Description>
              <LastState Value="1" Activated="0" RealAddress="00000000"/>
              <Color>FF00FF</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+32A1340</Address>
            </CheatEntry>
            <CheatEntry>
              <ID>3004</ID>
              <Description>"Extreme Range Enable (Set to 1)"</Description>
              <LastState Value="1" Activated="0" RealAddress="00000000"/>
              <Color>FF00FF</Color>
              <VariableType>Byte</VariableType>
              <Address>nba2k15.exe+32A1341</Address>
            </CheatEntry>
          </CheatEntries>
        </CheatEntry>
      </CheatEntries>
    </CheatEntry>
  </CheatEntries>
</CheatTable>
